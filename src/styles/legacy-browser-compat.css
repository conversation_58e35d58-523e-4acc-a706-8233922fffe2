/* Legacy Browser Compatibility Styles */
/* 为旧浏览器提供基本的样式支持 */

/* 基础重置 - 兼容 IE8+ */
.browser-compat-notification {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: 9999;
  max-width: 400px;
  width: 100%;
  background-color: #fbbf24; /* 黄色背景 */
  color: #000;
  padding: 16px;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* IE8 不支持 rgba，使用 filter 替代 */
.browser-compat-notification.ie8 {
  background-color: #fbbf24;
  filter: alpha(opacity=90);
}

/* 按钮样式 - 兼容旧浏览器 */
.browser-compat-button {
  display: inline-block;
  padding: 8px 12px;
  margin: 4px 4px 0 0;
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: inherit;
  text-decoration: none;
  cursor: pointer;
  font-size: 12px;
  font-family: inherit;
  transition: background-color 0.2s ease;
}

/* IE8 按钮样式 */
.browser-compat-button.ie8 {
  background-color: #ffffff;
  filter: alpha(opacity=20);
  border: 1px solid #cccccc;
}

.browser-compat-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.browser-compat-button.ie8:hover {
  background-color: #ffffff;
  filter: alpha(opacity=30);
}

/* 关闭按钮 */
.browser-compat-close {
  position: absolute;
  top: 8px;
  right: 12px;
  background: none;
  border: none;
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  color: inherit;
  opacity: 0.6;
}

.browser-compat-close:hover {
  opacity: 1;
}

/* 详情区域 */
.browser-compat-details {
  margin-top: 12px;
  padding: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 11px;
}

.browser-compat-details.ie8 {
  background-color: #ffffff;
  filter: alpha(opacity=10);
}

/* 浏览器升级对话框 */
.browser-upgrade-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.5);
}

.browser-upgrade-dialog.ie8 {
  background-color: #000000;
  filter: alpha(opacity=50);
}

.browser-upgrade-content {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90%;
  max-width: 600px;
  max-height: 90%;
  margin-top: -45%; /* 近似垂直居中 */
  margin-left: -45%; /* 近似水平居中 */
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 8px;
  padding: 24px;
  overflow-y: auto;
  font-family: Arial, sans-serif;
}

/* IE8 对话框内容 */
.browser-upgrade-content.ie8 {
  margin-top: -300px; /* 固定偏移 */
  margin-left: -300px; /* 固定偏移 */
}

/* 浏览器网格 - 使用 table 布局兼容旧浏览器 */
.browser-grid {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.browser-grid td {
  width: 50%;
  padding: 12px;
  border: 1px solid #e5e5e5;
  vertical-align: top;
}

.browser-item {
  display: block;
  text-decoration: none;
  color: inherit;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.browser-item:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.browser-icon {
  font-size: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.browser-name {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
}

.browser-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.browser-version {
  font-size: 11px;
  color: #999;
}

/* 动画降级 - 旧浏览器不支持 CSS 动画 */
@media screen and (min-width: 1px) {
  .browser-compat-notification {
    /* 简单的淡入效果，仅在支持的浏览器中生效 */
    animation: fadeInUp 0.3s ease-out;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 无动画降级 */
.no-animations .browser-compat-notification {
  animation: none;
}

/* 打印样式 */
@media print {
  .browser-compat-notification,
  .browser-upgrade-dialog {
    display: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .browser-compat-notification {
    border: 2px solid #000;
    background-color: #ffff00;
    color: #000;
  }
  
  .browser-compat-button {
    border: 2px solid #000;
    background-color: #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .browser-compat-notification {
    animation: none;
  }
  
  .browser-compat-button {
    transition: none;
  }
}

/* 小屏幕适配 */
@media screen and (max-width: 480px) {
  .browser-compat-notification {
    top: 8px;
    right: 8px;
    left: 8px;
    max-width: none;
  }
  
  .browser-upgrade-content {
    width: 95%;
    margin-left: -47.5%;
    padding: 16px;
  }
  
  .browser-grid td {
    width: 100%;
    display: block;
  }
}

/* 确保在所有浏览器中都有基本的可用性 */
.browser-compat-fallback {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #000;
  background-color: #fff;
}

/* 为不支持现代 CSS 的浏览器提供基础样式 */
.legacy-browser .browser-compat-notification {
  position: absolute;
  background-color: #ffcc00;
  border: 2px solid #ff9900;
  padding: 10px;
  margin: 10px;
  font-weight: bold;
}

.legacy-browser .browser-compat-button {
  background-color: #0066cc;
  color: #ffffff;
  padding: 5px 10px;
  margin: 2px;
  text-decoration: none;
  border: 1px solid #004499;
}

.legacy-browser .browser-upgrade-dialog {
  background-color: #ffffff;
  border: 3px solid #000000;
  padding: 20px;
  margin: 50px auto;
  width: 80%;
  max-width: 500px;
}

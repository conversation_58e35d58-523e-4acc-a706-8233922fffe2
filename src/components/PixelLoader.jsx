import React, { useEffect, useState } from 'react';
import { checkBrowserCompatibility } from '../utils/browserCompatibility';

const PixelLoader = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showPixels, setShowPixels] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('系统初始化中...');
  const [hasStarted, setHasStarted] = useState(false);

  // 浏览器兼容性检测函数
  const triggerBrowserCompatibilityCheck = async () => {
    try {
      console.log('[PixelLoader] 触发浏览器兼容性检测');

      // 检查必要的 API 是否存在
      if (typeof window === 'undefined' || typeof checkBrowserCompatibility !== 'function') {
        console.warn('[PixelLoader] 浏览器兼容性检测不可用');
        return;
      }

      const compatibilityResult = await checkBrowserCompatibility();

      // 检查是否支持自定义事件
      if (typeof CustomEvent !== 'undefined' && typeof window.dispatchEvent === 'function') {
        // 发送自定义事件，通知其他组件进行兼容性检测
        const event = new CustomEvent('browserCompatibilityChecked', {
          detail: compatibilityResult
        });
        window.dispatchEvent(event);
      } else {
        // 降级方案：直接在控制台输出结果
        console.log('[PixelLoader] 浏览器兼容性检测结果:', compatibilityResult);
      }

      console.log('[PixelLoader] 浏览器兼容性检测完成:', compatibilityResult);
    } catch (error) {
      console.error('[PixelLoader] 浏览器兼容性检测失败:', error);
      // 即使检测失败也不影响页面正常运行
    }
  };

  useEffect(() => {
    // 防止重复执行
    if (hasStarted) {
      console.log('[PixelLoader] 已经启动过，跳过重复执行');
      return;
    }

    console.log('[PixelLoader] 开始启动流程');
    setHasStarted(true);

    // 快速显示像素
    setTimeout(() => setShowPixels(true), 100);

    // 添加安全超时机制 - 确保页面一定会加载完成
    const safetyTimeout = setTimeout(() => {
      console.warn('[PixelLoader] 安全超时触发，强制完成加载');
      setProgress(100);
      setLoadingMessage('启动完成');
      setIsLoading(false);
      onComplete?.();
    }, 5000); // 5秒安全超时，缩短等待时间

    // 简化启动过程 - 跳过复杂的预加载
    const startSimpleLoad = async () => {
      try {
        // 模拟简单的加载过程
        setProgress(20);
        setLoadingMessage('系统初始化中...');
        
        await new Promise(resolve => setTimeout(resolve, 300));
        setProgress(50);
        setLoadingMessage('加载文档库...');
        
        await new Promise(resolve => setTimeout(resolve, 300));
        setProgress(80);
        setLoadingMessage('启动AI引擎...');
        
        await new Promise(resolve => setTimeout(resolve, 300));
        setProgress(100);
        setLoadingMessage('启动完成');

        setTimeout(() => {
          setIsLoading(false);
          onComplete?.();
          clearTimeout(safetyTimeout);

          // 在加载完成后触发浏览器兼容性检测
          setTimeout(() => {
            triggerBrowserCompatibilityCheck();
          }, 100); // 短暂延迟确保页面完全渲染
        }, 500);
        
      } catch (error) {
        console.error('[PixelLoader] 启动失败:', error);
        // 即使失败也要继续
        setProgress(100);
        setLoadingMessage('启动完成');
        setTimeout(() => {
          setIsLoading(false);
          onComplete?.();
          clearTimeout(safetyTimeout);

          // 在加载完成后触发浏览器兼容性检测
          setTimeout(() => {
            triggerBrowserCompatibilityCheck();
          }, 100); // 短暂延迟确保页面完全渲染
        }, 500);
      }
    };

    startSimpleLoad();

    // 清理函数
    return () => {
      clearTimeout(safetyTimeout);
    };
  }, []); // 移除onComplete依赖，防止重复执行

  // 简化的像素矩阵
  const textPixels = [
    [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
    [0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
    [0,0,0,1,0,1,0,0,0,1,1,0,0,1,0,0,1,1,0,0,1,0,0,0,1,0,1,0,0,0],
    [0,0,0,0,1,0,0,0,0,1,0,1,0,1,0,0,1,0,1,0,1,0,0,0,0,1,0,0,0,0],
    [0,0,0,0,1,0,0,0,0,1,0,0,1,1,0,0,1,0,0,1,1,0,0,0,1,0,1,0,0,0],
    [0,0,0,0,1,0,0,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
    [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
  ];

  const getLoadingText = (progress) => {
    if (progress < 20) return '系统初始化中...';
    if (progress < 50) return '加载文档库...';
    if (progress < 90) return '收集页面内容...';
    if (progress < 100) return '启动AI引擎...';
    return '即将进入系统...';
  };

  if (!isLoading) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black transition-opacity duration-500 ${isLoading ? 'opacity-100' : 'opacity-0'}`}>
      <div className="relative w-full h-full flex flex-col items-center justify-center">
        {/* 简化的背景效果 */}
        <div className="absolute inset-0 bg-gradient-radial from-cyan-500/10 to-transparent opacity-50"></div>

        {/* 像素网格 */}
        <div className="relative z-10 mb-8">
          <div 
            className="grid gap-1 p-4"
            style={{
              gridTemplateColumns: `repeat(${textPixels[0].length}, 1fr)`,
              gridTemplateRows: `repeat(${textPixels.length}, 1fr)`
            }}
          >
            {textPixels.map((row, rowIndex) => 
              row.map((pixel, colIndex) => {
                const delay = (colIndex * 0.03 + rowIndex * 0.01);
                const colorIndex = (colIndex + rowIndex) % 6;
                const gradients = [
                  'bg-gradient-to-br from-red-400 to-pink-500',
                  'bg-gradient-to-br from-orange-400 to-red-500', 
                  'bg-gradient-to-br from-yellow-400 to-orange-500',
                  'bg-gradient-to-br from-green-400 to-teal-500',
                  'bg-gradient-to-br from-cyan-400 to-blue-500',
                  'bg-gradient-to-br from-blue-400 to-purple-500'
                ];
                const shadows = [
                  '0 0 10px rgba(248, 113, 113, 0.8)',
                  '0 0 10px rgba(251, 146, 60, 0.8)',
                  '0 0 10px rgba(250, 204, 21, 0.8)', 
                  '0 0 10px rgba(52, 211, 153, 0.8)',
                  '0 0 10px rgba(6, 182, 212, 0.8)',
                  '0 0 10px rgba(139, 92, 246, 0.8)'
                ];
                return (
                  <div
                    key={`${rowIndex}-${colIndex}`}
                    className={`w-4 h-4 rounded-sm transition-all duration-300 ${
                      pixel === 1 && showPixels
                        ? `${gradients[colorIndex]} shadow-lg scale-100 opacity-100` 
                        : 'bg-gray-800/20 scale-75 opacity-30'
                    }`}
                    style={{
                      transitionDelay: showPixels ? `${delay}s` : '0s',
                      boxShadow: pixel === 1 && showPixels ? shadows[colorIndex] : 'none'
                    }}
                  />
                );
              })
            )}
          </div>
        </div>

        {/* 进度条 */}
        <div className="w-80 max-w-sm mx-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-mono bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-pulse">{loadingMessage}</span>
            <span className="text-sm font-mono bg-gradient-to-r from-green-400 to-cyan-300 bg-clip-text text-transparent font-semibold">{progress.toFixed(0)}%</span>
          </div>
          <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-green-400 via-cyan-400 via-blue-500 to-purple-500 rounded-full transition-all duration-200 shadow-lg shadow-cyan-500/50"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* 提示文字 */}
        <div className="mt-6 text-center">
          <p className="text-sm bg-gradient-to-r from-gray-300 via-cyan-200 to-blue-300 bg-clip-text text-transparent">
            YNNX AI 智能平台正在启动...
          </p>
        </div>
      </div>
    </div>
  );
};

export default PixelLoader; 
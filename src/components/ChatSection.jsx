import React, { useState, useEffect, useRef } from 'react';
import PixelLoader from './PixelLoader';
import chatAuthService from '../services/chatAuthService';

// LobeChat集成方案 - 使用nginx代理实现安全内嵌
const LobeChatApp = ({ onLoad, onError, user }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [authStage, setAuthStage] = useState('loading'); // 'loading', 'ready'
  const iframeRef = useRef(null);
  const loadingTimerRef = useRef(null);
  const progressTimerRef = useRef(null);

  // 安全的代理方案：通过nginx代理统一域名，隐藏真实LobeChat地址
  const chatUrl = `/chat/`;

  // 清理定时器和认证信息
  useEffect(() => {
    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
      if (progressTimerRef.current) {
        clearTimeout(progressTimerRef.current);
      }
      // 组件卸载时清理认证信息
      chatAuthService.clearChatAuth();
    };
  }, []);

  // 监听用户变化，更新认证信息
  useEffect(() => {
    if (user) {
      // 用户登录时设置认证信息
      setAuthHeaders();
    } else {
      // 用户注销时清理认证信息
      chatAuthService.clearChatAuth();
    }
  }, [user]);

  // 设置认证头部信息
  const setAuthHeaders = () => {
    if (user && iframeRef.current) {
      try {
        // 使用chatAuthService设置LobeChat会话
        const sessionData = chatAuthService.setChatSession(user);

        console.log('[ChatSection] LobeChat认证会话已设置:', sessionData);

        // 设置认证头部到document，供nginx代理读取
        const authHeaders = {
          'X-Auth-User': user.username || user.name,
          'X-Auth-Email': user.email || `${user.username}@ynnx.com`,
          'X-Auth-Name': user.name || user.username
        };

        // 将认证头部添加到页面的meta标签中
        Object.entries(authHeaders).forEach(([key, value]) => {
          let meta = document.querySelector(`meta[name="${key}"]`);
          if (!meta) {
            meta = document.createElement('meta');
            meta.name = key;
            document.head.appendChild(meta);
          }
          meta.content = value;
        });

        // 向iframe发送认证信息
        const authMessage = {
          type: 'YNNX_AUTH',
          data: sessionData,
          timestamp: new Date().getTime()
        };

        // 等待iframe加载完成后发送消息
        setTimeout(() => {
          if (iframeRef.current && iframeRef.current.contentWindow) {
            try {
              iframeRef.current.contentWindow.postMessage(authMessage, '*');
              console.log('[ChatSection] 认证消息已发送到LobeChat');
            } catch (e) {
              console.log('[ChatSection] 跨域消息发送受限，这是正常的安全行为');
            }
          }
        }, 2000);

      } catch (error) {
        console.error('[ChatSection] 设置认证信息失败:', error);
      }
    }
  };

  // iframe加载处理
  const handleIframeLoad = () => {
    console.log('Chat iframe onLoad 事件触发, 当前阶段:', authStage, '当前URL:', iframeRef.current?.src);

    // 只要iframe加载完成就处理，不限制authStage
    console.log('LobeChat通过nginx代理加载完成');
    setAuthStage('ready');
    setLoadingProgress(90);

    // 设置用户认证信息
    setAuthHeaders();

    // 清除所有计时器
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }

    // 给LobeChat时间完全渲染
    setTimeout(() => {
      console.log('LobeChat渲染完成，触发onLoad回调');
      setIsLoading(false);
      setHasError(false);
      setLoadingProgress(100);
      onLoad?.();
    }, 2000); // 减少等待时间到2秒
  };

  const handleIframeError = () => {
    console.log('Chat iframe onError 事件触发');
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  // 初始化流程 - 直接加载nginx代理的Chat
  useEffect(() => {
    if (authStage === 'loading' && iframeRef.current) {
      console.log('开始通过nginx代理加载LobeChat');
      setLoadingProgress(20);
      
      // 直接加载nginx代理的Chat，自动处理认证
      iframeRef.current.src = chatUrl;
    }
  }, [authStage, chatUrl]);

  // 加载进度更新
  useEffect(() => {
    if (authStage === 'loading') {
      setLoadingProgress(20);
    } else if (authStage === 'ready') {
      setLoadingProgress(100);
    }
  }, [authStage]);

  // 刷新功能
  const handleRefresh = () => {
    console.log('LobeChatApp内部刷新');

    // 清理所有定时器
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }
    if (progressTimerRef.current) {
      clearTimeout(progressTimerRef.current);
      progressTimerRef.current = null;
    }

    // 重置所有状态
    setIsLoading(true);
    setHasError(false);
    setAuthStage('loading');
    setLoadingProgress(0);

    // 清理认证信息
    chatAuthService.clearChatAuth();

    if (iframeRef.current) {
      // 先清空src，然后重新设置
      iframeRef.current.src = '';
      setTimeout(() => {
        if (iframeRef.current) {
          iframeRef.current.src = chatUrl;
        }
      }, 200);
    }
  };

  // 将刷新函数暴露给全局，供主组件调用
  useEffect(() => {
    window.triggerChatRefresh = handleRefresh;
    console.log('LobeChatApp刷新函数已注册');
    return () => {
      window.triggerChatRefresh = null;
      console.log('LobeChatApp刷新函数已清理');
    };
  }, []);

  return (
    <div className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden">
      {/* 加载状态覆盖层 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-900 flex flex-col items-center justify-center z-10">
          <div className="text-center">
            <div className="mb-8">
              <PixelLoader size="large" />
            </div>
            
            <div className="space-y-4">
              <div className="w-80 bg-gray-800 rounded-full h-2 overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-cyan-400 to-blue-500 transition-all duration-500 ease-out"
                  style={{ width: `${loadingProgress}%` }}
                />
              </div>
              
              <p className="text-sm text-gray-500 dark:text-gray-500">
                {authStage === 'loading' && "正在加载智能对话..."}
                {authStage === 'ready' && "即将完成..."}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-900 flex flex-col items-center justify-center z-10">
          <div className="text-center space-y-4">
            <div className="text-red-400 text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-semibold text-white">LobeChat 加载失败</h3>
            <p className="text-gray-400 max-w-md">
              无法连接到智能对话服务，请检查网络连接或稍后重试
            </p>
            <button
              onClick={handleRefresh}
              className="mt-4 px-6 py-2 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      )}
      
      {/* LobeChat iframe */}
      <iframe
        ref={iframeRef}
        className="chat-iframe w-full h-full border-0 bg-white dark:bg-gray-900"
        title="LobeChat 智能对话"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        // 安全沙箱设置：限制iframe权限，仅允许必要功能
        sandbox="allow-same-origin allow-scripts allow-forms allow-modals allow-pointer-lock allow-popups"
        // 权限策略：允许必要的功能
        allow="clipboard-read; clipboard-write; microphone; camera"
        referrerPolicy="no-referrer"
        loading="lazy"
        importance="high"
        style={{
          minHeight: '500px',
          background: 'white'
        }}
      />
    </div>
  );
};

// 主要的Chat Section组件
const ChatSection = ({ user, onLogin }) => {
  const [chatReady, setChatReady] = useState(false);
  const [chatError, setChatError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showGuide, setShowGuide] = useState(false);

  // 确保 useEffect 始终在相同位置调用
  useEffect(() => {
    const guideHidden = localStorage.getItem('chatGuideHidden');
    if (!guideHidden && user) {
      setShowGuide(true);
    }
  }, [user]);

  // 计算动态高度
  const calculateDynamicHeight = () => {
    const viewportHeight = window.innerHeight;
    const headerHeight = 80; // 导航栏高度
    const paddingHeight = 160; // 上下padding
    return Math.max(600, viewportHeight - headerHeight - paddingHeight);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleRefresh = () => {
    console.log('主组件触发刷新，当前状态:', { isRefreshing, chatReady, chatError });

    // 清除之前的超时
    if (window.chatRefreshTimeout) {
      clearTimeout(window.chatRefreshTimeout);
      window.chatRefreshTimeout = null;
    }

    setIsRefreshing(true);
    setChatError(null);

    // 设置超时保护
    const timeoutId = setTimeout(() => {
      console.log('刷新超时，重置状态');
      setIsRefreshing(false);
      setChatError('刷新超时，请重试');
    }, 30000);

    // 存储超时ID以便在成功时清除
    window.chatRefreshTimeout = timeoutId;

    // 触发LobeChatApp内部的刷新逻辑
    setTimeout(() => {
      if (window.triggerChatRefresh) {
        console.log('调用LobeChatApp刷新函数');
        window.triggerChatRefresh();
      } else {
        console.error('LobeChatApp刷新函数未找到');
        setIsRefreshing(false);
        setChatError('刷新功能初始化失败');
      }
    }, 100);
  };

  const hideGuide = () => {
    setShowGuide(false);
    localStorage.setItem('chatGuideHidden', 'true');
  };

  // 渲染未登录状态的UI
  const renderLoginPrompt = () => (
    <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8 text-center">
      <div className="space-y-6">
        <div className="text-6xl">💬</div>
        <div>
          <h3 className="text-2xl font-bold text-white mb-4">智能对话助手</h3>
          <p className="text-gray-400 mb-6">
            请先登录以使用 LobeChat 智能对话功能
          </p>
          <button
            onClick={onLogin}
            className="px-8 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold rounded-lg hover:from-cyan-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105"
          >
            立即登录
          </button>
        </div>
      </div>
    </div>
  );

  // 如果用户未登录，显示登录提示
  if (!user) {
    return renderLoginPrompt();
  }

  return (
    <div className={`chat-section ${isFullscreen ? 'fixed inset-0 z-50 bg-black' : 'relative'}`}>
      {/* 控制栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${chatReady ? 'bg-green-400' : 'bg-yellow-400'}`} />
            <span className="text-sm text-gray-400">
              {chatReady ? '智能对话已就绪' : '正在连接...'}
            </span>
          </div>
          
          {chatError && (
            <div className="text-sm text-red-400">
              连接失败: {chatError}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            {isRefreshing ? '刷新中...' : '刷新'}
          </button>
          
          <button
            onClick={toggleFullscreen}
            className="px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition-colors"
          >
            {isFullscreen ? '退出全屏' : '全屏模式'}
          </button>
        </div>
      </div>

      {/* 使用指南 */}
      {showGuide && (
        <div className="mb-6 bg-gradient-to-r from-cyan-900/30 to-blue-900/30 border border-cyan-700/50 rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="text-cyan-400 font-semibold mb-2">💡 智能对话使用指南</h4>
              <div className="text-sm text-gray-300 space-y-1">
                <p>• 需在设置中配置API密钥以使用对话功能</p>
                <p>• 可以进行代码生成、问题解答、创意写作等</p>
                <p>• 模型选择qwen3-235b-a22b</p>
                <p>• 您的对话数据已加密保护，确保隐私安全</p>
              </div>
            </div>
            <button
              onClick={hideGuide}
              className="text-xs text-cyan-400 hover:text-cyan-300 font-medium whitespace-nowrap ml-4"
            >
              我知道了
            </button>
          </div>
        </div>
      )}

      {/* Chat Container */}
      <div
        className={`
          chat-container
          ${isFullscreen ? 'h-full' : ''}
          bg-white dark:bg-gray-900 transition-all duration-300 rounded-lg overflow-hidden
        `}
        style={!isFullscreen ? {
          height: `${calculateDynamicHeight()}px`,
          minHeight: '600px'
        } : {}}
      >
        <LobeChatApp
          user={user}
          onLoad={() => {
            console.log('LobeChat 加载完成，重置状态');
            // 清除超时保护
            if (window.chatRefreshTimeout) {
              clearTimeout(window.chatRefreshTimeout);
              window.chatRefreshTimeout = null;
            }
            setChatReady(true);
            setChatError(null);
            setIsRefreshing(false);
          }}
          onError={(error) => {
            console.error('LobeChat 加载错误:', error);
            // 清除超时保护
            if (window.chatRefreshTimeout) {
              clearTimeout(window.chatRefreshTimeout);
              window.chatRefreshTimeout = null;
            }
            setChatError(error?.message || '加载失败');
            setChatReady(false);
            setIsRefreshing(false);
          }}
        />
      </div>
    </div>
  );
};

export default ChatSection;

/**
 * 浏览器兼容性开发者工具
 * 提供调试和测试功能
 */

import { 
  checkBrowserCompatibility, 
  forceCheckBrowserCompatibility,
  getDetectionStatistics,
  clearDetectionCache,
  enablePerformanceMonitoring,
  disablePerformanceMonitoring
} from './browserCompatibility.js';
import browserPerformanceMonitor from './browserPerformanceMonitor.js';
import smartDetectionStrategy from './smartDetectionStrategy.js';

class BrowserCompatibilityDevTools {
  constructor() {
    this.isEnabled = this.checkIfEnabled();
    this.init();
  }

  /**
   * 检查是否启用开发者工具
   */
  checkIfEnabled() {
    return process.env.NODE_ENV === 'development' || 
           window.location.search.includes('debug=true') ||
           localStorage.getItem('browser-devtools-enabled') === 'true';
  }

  /**
   * 初始化开发者工具
   */
  init() {
    if (!this.isEnabled) return;

    this.setupConsoleCommands();
    this.setupKeyboardShortcuts();
    this.addDebugPanel();
    
    console.log('%c🔧 Browser Compatibility DevTools Enabled', 
      'color: #4CAF50; font-weight: bold; font-size: 14px;');
    console.log('Available commands:', this.getAvailableCommands());
  }

  /**
   * 设置控制台命令
   */
  setupConsoleCommands() {
    // 全局命令对象
    window.browserCompatDevTools = {
      // 基础检测命令
      check: () => checkBrowserCompatibility(),
      forceCheck: () => forceCheckBrowserCompatibility(),
      
      // 统计和监控
      stats: () => getDetectionStatistics(),
      report: () => browserPerformanceMonitor.getReport(),
      
      // 缓存管理
      clearCache: () => clearDetectionCache(),
      clearData: () => browserPerformanceMonitor.constructor.clearData(),
      
      // 监控控制
      enableMonitoring: () => enablePerformanceMonitoring(),
      disableMonitoring: () => disablePerformanceMonitoring(),
      
      // 模拟测试
      simulateOldBrowser: (browserName, version) => this.simulateOldBrowser(browserName, version),
      simulateSlowNetwork: (delay) => this.simulateSlowNetwork(delay),
      simulateError: (type) => this.simulateError(type),
      
      // 调试工具
      debug: {
        showDetectionFlow: () => this.showDetectionFlow(),
        analyzeUserAgent: (ua) => this.analyzeUserAgent(ua),
        testFeatures: () => this.testAllFeatures(),
        benchmark: () => this.runBenchmark()
      },
      
      // 配置管理
      config: {
        get: () => smartDetectionStrategy.adaptiveThresholds,
        set: (key, value) => this.setConfig(key, value),
        reset: () => smartDetectionStrategy.recalculateThresholds()
      },
      
      // 帮助
      help: () => this.showHelp()
    };

    // 简化命令别名
    window.bcdt = window.browserCompatDevTools;
  }

  /**
   * 设置键盘快捷键
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      // Ctrl+Shift+B: 打开调试面板
      if (event.ctrlKey && event.shiftKey && event.key === 'B') {
        event.preventDefault();
        this.toggleDebugPanel();
      }
      
      // Ctrl+Shift+C: 强制检测
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        console.log('Force checking browser compatibility...');
        forceCheckBrowserCompatibility().then(result => {
          console.log('Force check result:', result);
        });
      }
    });
  }

  /**
   * 添加调试面板
   */
  addDebugPanel() {
    const panel = document.createElement('div');
    panel.id = 'browser-compat-debug-panel';
    panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 300px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 15px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      display: none;
      max-height: 400px;
      overflow-y: auto;
    `;

    panel.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <strong>Browser Compat Debug</strong>
        <button onclick="this.parentElement.parentElement.style.display='none'" 
                style="background: none; border: none; color: white; cursor: pointer;">×</button>
      </div>
      <div id="debug-content">
        <div>Press Ctrl+Shift+B to toggle</div>
        <div>Press Ctrl+Shift+C to force check</div>
        <div style="margin-top: 10px;">
          <button onclick="window.bcdt.check()" style="margin: 2px; padding: 4px 8px; font-size: 10px;">Check</button>
          <button onclick="window.bcdt.stats()" style="margin: 2px; padding: 4px 8px; font-size: 10px;">Stats</button>
          <button onclick="window.bcdt.clearCache()" style="margin: 2px; padding: 4px 8px; font-size: 10px;">Clear</button>
        </div>
      </div>
    `;

    document.body.appendChild(panel);
    this.debugPanel = panel;
  }

  /**
   * 切换调试面板
   */
  toggleDebugPanel() {
    if (this.debugPanel) {
      const isVisible = this.debugPanel.style.display !== 'none';
      this.debugPanel.style.display = isVisible ? 'none' : 'block';
      
      if (!isVisible) {
        this.updateDebugPanel();
      }
    }
  }

  /**
   * 更新调试面板内容
   */
  updateDebugPanel() {
    if (!this.debugPanel) return;

    const stats = getDetectionStatistics();
    const content = this.debugPanel.querySelector('#debug-content');
    
    content.innerHTML = `
      <div><strong>Cache Hit Rate:</strong> ${(stats.cacheHitRate * 100).toFixed(1)}%</div>
      <div><strong>Avg Detection:</strong> ${stats.avgDetectionTime.toFixed(0)}ms</div>
      <div><strong>Total Detections:</strong> ${stats.totalDetections}</div>
      <div><strong>Recent (7d):</strong> ${stats.recentDetections}</div>
      <div style="margin-top: 10px;">
        <button onclick="window.bcdt.check().then(r => console.log(r))" 
                style="margin: 2px; padding: 4px 8px; font-size: 10px;">Check</button>
        <button onclick="console.log(window.bcdt.stats())" 
                style="margin: 2px; padding: 4px 8px; font-size: 10px;">Stats</button>
        <button onclick="window.bcdt.clearCache(); alert('Cache cleared')" 
                style="margin: 2px; padding: 4px 8px; font-size: 10px;">Clear</button>
      </div>
      <div style="margin-top: 10px; font-size: 10px; color: #ccc;">
        Use console: window.bcdt or window.browserCompatDevTools
      </div>
    `;
  }

  /**
   * 模拟旧浏览器
   */
  simulateOldBrowser(browserName = 'Chrome', version = 50) {
    const originalUserAgent = navigator.userAgent;
    
    // 创建模拟的 userAgent
    const simulatedUA = originalUserAgent.replace(
      /Chrome\/\d+/g, 
      `${browserName}/${version}`
    );

    console.log(`Simulating ${browserName} ${version}`);
    console.log('Original UA:', originalUserAgent);
    console.log('Simulated UA:', simulatedUA);

    // 临时替换 userAgent（仅用于测试）
    Object.defineProperty(navigator, 'userAgent', {
      get: () => simulatedUA,
      configurable: true
    });

    // 运行检测
    const result = forceCheckBrowserCompatibility();
    console.log('Simulation result:', result);

    // 恢复原始 userAgent
    Object.defineProperty(navigator, 'userAgent', {
      get: () => originalUserAgent,
      configurable: true
    });

    return result;
  }

  /**
   * 模拟慢网络
   */
  simulateSlowNetwork(delay = 1000) {
    console.log(`Simulating slow network with ${delay}ms delay`);
    
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(originalFetch.apply(this, args));
        }, delay);
      });
    };

    // 5秒后恢复
    setTimeout(() => {
      window.fetch = originalFetch;
      console.log('Network simulation ended');
    }, 5000);
  }

  /**
   * 模拟错误
   */
  simulateError(type = 'detection') {
    switch (type) {
      case 'detection':
        throw new Error('Simulated detection error');
      case 'storage':
        localStorage.setItem = () => { throw new Error('Storage error'); };
        break;
      case 'performance':
        window.performance = undefined;
        break;
      default:
        console.error('Unknown error type:', type);
    }
  }

  /**
   * 显示检测流程
   */
  showDetectionFlow() {
    console.group('🔍 Browser Compatibility Detection Flow');
    
    console.log('1. Smart Detection Strategy Check');
    const decision = smartDetectionStrategy.shouldRunDetection(navigator.userAgent);
    console.log('Decision:', decision);
    
    if (decision.shouldRun) {
      console.log('2. Running Full Detection');
      const result = forceCheckBrowserCompatibility();
      console.log('Result:', result);
    } else {
      console.log('2. Using Cached/Optimized Result');
      console.log('Cached Result:', decision.result);
    }
    
    console.log('3. Performance Metrics');
    console.log('Statistics:', getDetectionStatistics());
    
    console.groupEnd();
  }

  /**
   * 分析用户代理
   */
  analyzeUserAgent(userAgent = navigator.userAgent) {
    console.group('🔍 User Agent Analysis');
    console.log('User Agent:', userAgent);
    
    // 导入并使用浏览器检测逻辑
    import('./browserCompatibility.js').then(module => {
      const browserInfo = module.getBrowserInfo();
      console.log('Parsed Browser Info:', browserInfo);
    });
    
    console.groupEnd();
  }

  /**
   * 测试所有特性
   */
  testAllFeatures() {
    console.group('🧪 Feature Support Test');
    
    import('./browserCompatibility.js').then(module => {
      const features = module.checkFeatureSupport();
      
      Object.entries(features).forEach(([feature, supported]) => {
        const icon = supported ? '✅' : '❌';
        console.log(`${icon} ${feature}: ${supported}`);
      });
    });
    
    console.groupEnd();
  }

  /**
   * 运行性能基准测试
   */
  async runBenchmark() {
    console.group('⚡ Performance Benchmark');
    
    const iterations = 10;
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      await forceCheckBrowserCompatibility();
      const end = performance.now();
      times.push(end - start);
    }
    
    const avg = times.reduce((a, b) => a + b, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    
    console.log(`Average: ${avg.toFixed(2)}ms`);
    console.log(`Min: ${min.toFixed(2)}ms`);
    console.log(`Max: ${max.toFixed(2)}ms`);
    console.log('All times:', times.map(t => t.toFixed(2) + 'ms'));
    
    console.groupEnd();
  }

  /**
   * 设置配置
   */
  setConfig(key, value) {
    if (smartDetectionStrategy.adaptiveThresholds.hasOwnProperty(key)) {
      smartDetectionStrategy.adaptiveThresholds[key] = value;
      console.log(`Config updated: ${key} = ${value}`);
    } else {
      console.error(`Unknown config key: ${key}`);
    }
  }

  /**
   * 获取可用命令
   */
  getAvailableCommands() {
    return [
      'bcdt.check() - Run compatibility check',
      'bcdt.forceCheck() - Force full check',
      'bcdt.stats() - Get statistics',
      'bcdt.clearCache() - Clear cache',
      'bcdt.debug.showDetectionFlow() - Show detection flow',
      'bcdt.debug.testFeatures() - Test all features',
      'bcdt.debug.benchmark() - Run benchmark',
      'bcdt.help() - Show help'
    ];
  }

  /**
   * 显示帮助
   */
  showHelp() {
    console.group('🔧 Browser Compatibility DevTools Help');
    console.log('Keyboard Shortcuts:');
    console.log('  Ctrl+Shift+B - Toggle debug panel');
    console.log('  Ctrl+Shift+C - Force compatibility check');
    console.log('');
    console.log('Console Commands:');
    this.getAvailableCommands().forEach(cmd => console.log('  ' + cmd));
    console.log('');
    console.log('Debug Panel: Fixed position panel with quick actions');
    console.log('Performance Monitoring: Automatic data collection in dev mode');
    console.groupEnd();
  }

  /**
   * 启用开发者工具
   */
  static enable() {
    localStorage.setItem('browser-devtools-enabled', 'true');
    window.location.reload();
  }

  /**
   * 禁用开发者工具
   */
  static disable() {
    localStorage.removeItem('browser-devtools-enabled');
    delete window.browserCompatDevTools;
    delete window.bcdt;
    console.log('Browser Compatibility DevTools disabled');
  }
}

// 自动初始化
const devTools = new BrowserCompatibilityDevTools();

export default devTools;
export { BrowserCompatibilityDevTools };

/**
 * 资源加载监控器
 * 监控CSS、字体、图标等资源的加载状态，检测兼容性问题
 */

class ResourceLoadingMonitor {
  constructor() {
    this.loadedResources = new Set();
    this.failedResources = new Set();
    this.resourceChecks = new Map();
    this.isMonitoring = false;
    this.compatibilityIssues = [];

    this.init();
  }

  /**
   * 检查是否应该启用监控
   */
  shouldEnableMonitoring() {
    // 在内网环境中，资源监控容易误报，更加严格的启用条件：
    // 1. 明确的debug参数
    // 2. localStorage明确启用
    // 3. 检测到真正的旧浏览器（IE或非常旧的版本）

    const hasDebugParam = window.location.search.includes('debug=true') ||
                         window.location.search.includes('monitor=true') ||
                         window.location.search.includes('resource-monitor=true');
    const isExplicitlyEnabled = localStorage.getItem('resource-monitoring-enabled') === 'true';

    // 检测是否为真正需要监控的旧浏览器
    const isOldBrowser = this.detectOldBrowser();

    // 内网环境默认禁用，除非明确启用
    const shouldEnable = hasDebugParam || isExplicitlyEnabled || isOldBrowser;

    console.log('[ResourceMonitor] 监控启用检查 (内网模式):', {
      hasDebugParam,
      isExplicitlyEnabled,
      isOldBrowser,
      shouldEnable,
      reason: shouldEnable ?
        (hasDebugParam ? 'debug参数' : isExplicitlyEnabled ? '明确启用' : '旧浏览器') :
        '内网环境默认禁用'
    });

    return shouldEnable;
  }

  /**
   * 检测是否为旧浏览器（需要资源监控）
   */
  detectOldBrowser() {
    const ua = navigator.userAgent.toLowerCase();

    // IE浏览器 - 绝对需要监控
    if (ua.indexOf('trident') > -1 || ua.indexOf('msie') > -1) {
      console.log('[ResourceMonitor] 检测到IE浏览器，启用监控');
      return true;
    }

    // 非常旧的Chrome（小于60）
    const chromeMatch = ua.match(/chrome\/(\d+)/);
    if (chromeMatch && parseInt(chromeMatch[1]) < 60) {
      console.log(`[ResourceMonitor] 检测到旧版Chrome ${chromeMatch[1]}，启用监控`);
      return true;
    }

    // 非常旧的Firefox（小于60）
    const firefoxMatch = ua.match(/firefox\/(\d+)/);
    if (firefoxMatch && parseInt(firefoxMatch[1]) < 60) {
      console.log(`[ResourceMonitor] 检测到旧版Firefox ${firefoxMatch[1]}，启用监控`);
      return true;
    }

    // 非常旧的Safari（小于12）
    const safariMatch = ua.match(/version\/(\d+).*safari/);
    if (safariMatch && parseInt(safariMatch[1]) < 12) {
      console.log(`[ResourceMonitor] 检测到旧版Safari ${safariMatch[1]}，启用监控`);
      return true;
    }

    // 其他现代浏览器不需要监控
    console.log('[ResourceMonitor] 检测到现代浏览器，不需要资源监控');
    return false;
  }

  /**
   * 初始化监控
   */
  init() {
    if (typeof window === 'undefined') return;

    // 检查是否应该启用监控（只在开发环境或明确启用时运行）
    const shouldEnable = this.shouldEnableMonitoring();
    if (!shouldEnable) {
      console.log('[ResourceMonitor] 监控已禁用，跳过初始化');
      return;
    }

    this.setupResourceMonitoring();
    this.setupFontMonitoring();
    this.setupIconMonitoring();
    this.setupPerformanceObserver();

    // 延迟检查，确保页面完全加载完成
    setTimeout(() => {
      this.performCompatibilityCheck();
    }, 5000); // 增加延迟时间，确保所有资源加载完成
  }

  /**
   * 设置资源加载监控
   */
  setupResourceMonitoring() {
    // 监控所有资源加载事件
    window.addEventListener('load', () => {
      this.checkAllResources();
    });

    // 监控资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target && event.target !== window) {
        this.handleResourceError(event.target, event);
      }
    }, true);

    // 监控CSS加载
    document.addEventListener('DOMContentLoaded', () => {
      this.checkCSSResources();
    });
  }

  /**
   * 设置字体监控
   */
  setupFontMonitoring() {
    if ('fonts' in document) {
      // 现代浏览器的字体API
      document.fonts.ready.then(() => {
        this.checkFontLoading();
      }).catch((error) => {
        console.warn('Font loading check failed:', error);
        this.addCompatibilityIssue('font-api', '字体API不支持，可能影响图标显示');
      });
    } else {
      // 旧浏览器的降级检查
      setTimeout(() => {
        this.checkFontLoadingFallback();
      }, 1000);
    }
  }

  /**
   * 设置图标监控
   */
  setupIconMonitoring() {
    // 检查Font Awesome图标
    setTimeout(() => {
      this.checkFontAwesomeIcons();
    }, 1500);

    // 检查其他图标库
    setTimeout(() => {
      this.checkOtherIcons();
    }, 2000);
  }

  /**
   * 设置性能观察器
   */
  setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.name.includes('font') || entry.name.includes('css')) {
              this.analyzeResourcePerformance(entry);
            }
          });
        });
        observer.observe({ entryTypes: ['resource'] });
      } catch (error) {
        console.warn('PerformanceObserver setup failed:', error);
      }
    }
  }

  /**
   * 处理资源加载错误
   */
  handleResourceError(element, event) {
    const resourceInfo = {
      type: element.tagName.toLowerCase(),
      src: element.src || element.href,
      error: event.type,
      timestamp: Date.now()
    };

    this.failedResources.add(resourceInfo);
    
    // 特殊处理字体和CSS文件
    if (resourceInfo.src) {
      if (resourceInfo.src.includes('font') || resourceInfo.src.includes('.woff')) {
        this.addCompatibilityIssue('font-loading', `字体文件加载失败: ${resourceInfo.src}`);
      } else if (resourceInfo.src.includes('.css')) {
        this.addCompatibilityIssue('css-loading', `CSS文件加载失败: ${resourceInfo.src}`);
      }
    }

    console.warn('Resource loading failed:', resourceInfo);
  }

  /**
   * 检查所有资源
   */
  checkAllResources() {
    const resources = performance.getEntriesByType('resource');
    
    resources.forEach(resource => {
      if (resource.name.includes('font-awesome') || resource.name.includes('fa-')) {
        if (resource.responseEnd === 0) {
          this.addCompatibilityIssue('fontawesome-loading', 'Font Awesome资源加载失败');
        }
      }
    });
  }

  /**
   * 检查CSS资源
   */
  checkCSSResources() {
    const stylesheets = document.styleSheets;
    let fontAwesomeFound = false;
    let fontAwesomeAccessible = false;

    for (let i = 0; i < stylesheets.length; i++) {
      try {
        const sheet = stylesheets[i];
        if (sheet.href) {
          // 检查多种可能的Font Awesome文件名模式
          const isFontAwesome =
            sheet.href.includes('font-awesome') ||
            sheet.href.includes('fontawesome') ||
            sheet.href.includes('/assets/fonts/') ||
            sheet.href.includes('optimized-fonts') ||
            sheet.href.includes('fa-');

          if (isFontAwesome) {
            fontAwesomeFound = true;
            console.log('[ResourceMonitor] 找到Font Awesome相关样式表:', sheet.href);

            try {
              // 尝试访问CSS规则来验证加载
              const rules = sheet.cssRules || sheet.rules;
              if (rules && rules.length > 0) {
                fontAwesomeAccessible = true;
                console.log('[ResourceMonitor] Font Awesome CSS规则可访问，规则数量:', rules.length);
              }
            } catch (accessError) {
              if (accessError.name === 'SecurityError') {
                // 跨域CSS，无法检查规则，但样式表存在，假设正常
                fontAwesomeAccessible = true;
                console.log('[ResourceMonitor] Font Awesome CSS跨域，假设正常加载');
              }
            }
          }
        }
      } catch (error) {
        console.warn('[ResourceMonitor] CSS检查错误:', error);
      }
    }

    // 进一步检查是否通过其他方式加载
    if (!fontAwesomeFound) {
      const hasInlineFA = this.checkInlineFontAwesome();
      const hasWorkingIcons = this.checkFontAwesomeActuallyWorking();

      if (!hasInlineFA && !hasWorkingIcons) {
        console.warn('[ResourceMonitor] 未找到Font Awesome样式表且图标不工作');
        this.addCompatibilityIssue('fontawesome-missing', 'Font Awesome样式表未找到');
      } else {
        console.log('[ResourceMonitor] Font Awesome通过其他方式加载或图标正常工作');
        fontAwesomeFound = true;
        fontAwesomeAccessible = true;
      }
    } else if (!fontAwesomeAccessible) {
      // 找到了样式表但无法访问，检查图标是否实际工作
      const hasWorkingIcons = this.checkFontAwesomeActuallyWorking();
      if (hasWorkingIcons) {
        console.log('[ResourceMonitor] 虽然无法访问CSS规则，但图标正常工作');
        fontAwesomeAccessible = true;
      } else {
        console.warn('[ResourceMonitor] Font Awesome样式表无法访问且图标不工作');
        this.addCompatibilityIssue('fontawesome-css', 'Font Awesome CSS规则无法访问');
      }
    }
  }

  /**
   * 检查内联Font Awesome
   */
  checkInlineFontAwesome() {
    // 检查是否有内联的Font Awesome样式
    const styles = document.querySelectorAll('style');
    for (const style of styles) {
      if (style.textContent && (
        style.textContent.includes('font-awesome') ||
        style.textContent.includes('fontawesome') ||
        style.textContent.includes('fa-solid') ||
        style.textContent.includes('.fas') ||
        style.textContent.includes('.far') ||
        style.textContent.includes('.fab')
      )) {
        console.log('[ResourceMonitor] 找到内联Font Awesome样式');
        return true;
      }
    }

    return false;
  }

  /**
   * 检查Font Awesome是否实际工作
   */
  checkFontAwesomeActuallyWorking() {
    // 检查页面中是否有Font Awesome图标元素
    const faElements = document.querySelectorAll('.fas, .far, .fab, .fal, .fad, .fa');

    if (faElements.length === 0) {
      console.log('[ResourceMonitor] 页面中没有Font Awesome图标元素');
      return true; // 没有使用Font Awesome，不算问题
    }

    console.log(`[ResourceMonitor] 找到 ${faElements.length} 个Font Awesome图标元素`);

    // 检查前几个图标是否有正确的字体
    let workingCount = 0;
    const checkCount = Math.min(5, faElements.length); // 只检查前5个

    for (let i = 0; i < checkCount; i++) {
      const element = faElements[i];
      try {
        const computedStyle = window.getComputedStyle(element);
        const beforeStyle = window.getComputedStyle(element, '::before');

        const fontFamily = computedStyle.fontFamily || beforeStyle.fontFamily;
        const content = beforeStyle.content;

        // 检查是否有Font Awesome字体或内容
        const hasFAFont = fontFamily && (
          fontFamily.includes('Font Awesome') ||
          fontFamily.includes('FontAwesome') ||
          fontFamily.includes('fa-solid') ||
          fontFamily.includes('fa-regular') ||
          fontFamily.includes('fa-brands')
        );

        const hasContent = content && content !== 'none' && content !== '""' && content !== 'normal';

        if (hasFAFont || hasContent) {
          workingCount++;
          console.log(`[ResourceMonitor] 图标 ${i} 工作正常:`, {
            fontFamily,
            content,
            className: element.className
          });
        }
      } catch (error) {
        console.warn(`[ResourceMonitor] 检查图标 ${i} 时出错:`, error);
      }
    }

    const workingRate = workingCount / checkCount;
    const isWorking = workingRate >= 0.5; // 50%以上的图标工作正常就认为OK

    console.log(`[ResourceMonitor] Font Awesome工作状态: ${workingCount}/${checkCount} 图标正常 (${(workingRate * 100).toFixed(1)}%)`);

    return isWorking;
  }

  /**
   * 检查字体加载
   */
  checkFontLoading() {
    const fontFaces = document.fonts;
    let fontAwesomeLoaded = false;

    fontFaces.forEach(font => {
      if (font.family.includes('Font Awesome') || font.family.includes('FontAwesome')) {
        if (font.status === 'loaded') {
          fontAwesomeLoaded = true;
        } else if (font.status === 'error') {
          this.addCompatibilityIssue('fontawesome-font', 'Font Awesome字体加载失败');
        }
      }
    });

    if (!fontAwesomeLoaded) {
      // 进一步检查
      this.checkFontAwesomeAvailability();
    }
  }

  /**
   * 旧浏览器的字体检查降级方案
   */
  checkFontLoadingFallback() {
    // 创建测试元素检查字体是否可用
    const testElement = document.createElement('span');
    testElement.style.fontFamily = 'FontAwesome, "Font Awesome 5 Free"';
    testElement.style.fontSize = '16px';
    testElement.innerHTML = '&#xf084;'; // Font Awesome key icon
    testElement.style.position = 'absolute';
    testElement.style.left = '-9999px';
    
    document.body.appendChild(testElement);
    
    setTimeout(() => {
      const computedStyle = window.getComputedStyle(testElement);
      const fontFamily = computedStyle.fontFamily;
      
      if (!fontFamily.includes('FontAwesome') && !fontFamily.includes('Font Awesome')) {
        this.addCompatibilityIssue('fontawesome-fallback', '字体降级检查：Font Awesome字体不可用');
      }
      
      document.body.removeChild(testElement);
    }, 500);
  }

  /**
   * 检查Font Awesome图标
   */
  checkFontAwesomeIcons() {
    const faIcons = document.querySelectorAll('.fas, .far, .fab, .fal, .fad');

    if (faIcons.length === 0) {
      console.log('[ResourceMonitor] 页面没有使用Font Awesome图标，跳过检查');
      return; // 页面没有使用Font Awesome图标
    }

    console.log(`[ResourceMonitor] 检查 ${faIcons.length} 个Font Awesome图标`);

    let visibleIcons = 0;
    let problematicIcons = 0;
    let fontIssues = 0;

    faIcons.forEach((icon, index) => {
      const rect = icon.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(icon, '::before');
      const elementStyle = window.getComputedStyle(icon);

      // 检查图标是否可见（更宽松的检查）
      const isVisible = rect.width > 0 && rect.height > 0;
      if (isVisible) {
        visibleIcons++;
      }

      // 检查字体是否正确应用（更准确的检查）
      const fontFamily = elementStyle.fontFamily;
      const beforeContent = computedStyle.content;

      const hasFAFont = fontFamily && (
        fontFamily.includes('Font Awesome') ||
        fontFamily.includes('FontAwesome') ||
        fontFamily.includes('"Font Awesome')
      );

      const hasContent = beforeContent && beforeContent !== 'none' && beforeContent !== '""';

      // 只有当既没有正确字体又没有内容时才认为有问题
      if (!hasFAFont && !hasContent && isVisible) {
        fontIssues++;

        // 添加调试信息到元素
        icon.setAttribute('data-font-issue', 'true');
        icon.title = `字体问题: ${fontFamily}, 内容: ${beforeContent}`;

        console.warn(`[ResourceMonitor] 图标 ${index} 有问题:`, {
          fontFamily,
          beforeContent,
          isVisible,
          className: icon.className
        });
      }
    });

    // 只有当大部分图标都有问题时才报告（避免误报）
    const problemThreshold = 0.5; // 50%的图标有问题才报告
    const problemRate = fontIssues / faIcons.length;

    if (problemRate > problemThreshold) {
      const issueRate = (fontIssues / faIcons.length * 100).toFixed(1);
      console.warn(`[ResourceMonitor] Font Awesome图标问题率过高: ${issueRate}%`);
      this.addCompatibilityIssue('fontawesome-display',
        `${fontIssues}/${faIcons.length} 个Font Awesome图标显示异常 (${issueRate}%)`);
    } else {
      console.log(`[ResourceMonitor] Font Awesome图标检查通过: ${visibleIcons}/${faIcons.length} 图标正常，问题率: ${(problemRate * 100).toFixed(1)}%`);
    }
  }

  /**
   * 检查Font Awesome可用性
   */
  checkFontAwesomeAvailability() {
    // 检查是否有Font Awesome的CSS类定义
    const testElement = document.createElement('i');
    testElement.className = 'fas fa-test';
    testElement.style.position = 'absolute';
    testElement.style.left = '-9999px';
    
    document.body.appendChild(testElement);
    
    setTimeout(() => {
      const computedStyle = window.getComputedStyle(testElement, '::before');
      const content = computedStyle.content;
      
      if (!content || content === 'none' || content === '""') {
        this.addCompatibilityIssue('fontawesome-css-missing', 'Font Awesome CSS类未正确加载');
      }
      
      document.body.removeChild(testElement);
    }, 100);
  }

  /**
   * 检查其他图标
   */
  checkOtherIcons() {
    // 检查Lucide React图标（如果使用）
    const lucideIcons = document.querySelectorAll('[data-lucide]');
    if (lucideIcons.length > 0) {
      this.checkLucideIcons(lucideIcons);
    }

    // 检查SVG图标
    const svgIcons = document.querySelectorAll('svg');
    if (svgIcons.length > 0) {
      this.checkSVGIcons(svgIcons);
    }
  }

  /**
   * 检查Lucide图标
   */
  checkLucideIcons(icons) {
    let problematicIcons = 0;
    
    icons.forEach(icon => {
      const rect = icon.getBoundingClientRect();
      if (rect.width === 0 || rect.height === 0) {
        problematicIcons++;
      }
    });

    if (problematicIcons > 0) {
      this.addCompatibilityIssue('lucide-display', 
        `${problematicIcons}/${icons.length} 个Lucide图标显示异常`);
    }
  }

  /**
   * 检查SVG图标
   */
  checkSVGIcons(svgs) {
    let problematicSVGs = 0;
    
    svgs.forEach(svg => {
      // 检查SVG是否支持
      if (!document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure", "1.1")) {
        problematicSVGs++;
      }
    });

    if (problematicSVGs > 0) {
      this.addCompatibilityIssue('svg-support', 'SVG图标支持有问题');
    }
  }

  /**
   * 分析资源性能
   */
  analyzeResourcePerformance(entry) {
    if (entry.duration > 3000) { // 超过3秒
      this.addCompatibilityIssue('resource-slow', 
        `资源加载缓慢: ${entry.name} (${entry.duration.toFixed(0)}ms)`);
    }

    if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
      // 可能来自缓存，但也可能是加载失败
      this.resourceChecks.set(entry.name, 'cache-or-failed');
    }
  }

  /**
   * 添加兼容性问题
   */
  addCompatibilityIssue(type, message) {
    const issue = {
      type,
      message,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    };

    this.compatibilityIssues.push(issue);
    console.warn(`[ResourceMonitor] ${type}: ${message}`);

    // 触发兼容性警告事件
    this.triggerCompatibilityWarning(issue);
  }

  /**
   * 触发兼容性警告
   */
  triggerCompatibilityWarning(issue) {
    // 发送自定义事件通知兼容性检查组件
    const event = new CustomEvent('resourceCompatibilityIssue', {
      detail: {
        issue,
        allIssues: this.compatibilityIssues,
        summary: this.getCompatibilitySummary()
      }
    });

    window.dispatchEvent(event);
  }

  /**
   * 执行兼容性检查
   */
  performCompatibilityCheck() {
    console.log('[ResourceMonitor] 执行资源兼容性检查...');
    
    // 检查关键资源
    this.checkCriticalResources();
    
    // 生成兼容性报告
    const report = this.generateCompatibilityReport();
    
    if (report.hasIssues) {
      console.warn('[ResourceMonitor] 发现资源加载问题:', report);

      // 只有严重的资源问题才触发兼容性检查事件
      // 一般的资源问题通过 resourceCompatibilityIssue 事件处理
      if (report.hasCriticalIssues) {
        const event = new CustomEvent('browserCompatibilityChecked', {
          detail: {
            isCompatible: false, // 严重资源问题影响兼容性
            isRecommendedVersion: true, // 资源问题不影响浏览器版本推荐状态
            browser: this.getBrowserInfo(),
            issues: {
              resourceIssues: this.compatibilityIssues,
              hasResourceIssues: true,
              versionTooOld: false,
              unsupportedFeatures: ['资源加载失败']
            },
            resourceReport: report
          }
        });

        window.dispatchEvent(event);
      } else {
        // 非严重问题只触发资源兼容性问题事件
        console.log('[ResourceMonitor] 资源问题不严重，不影响整体兼容性');
      }
    }
  }

  /**
   * 检查关键资源
   */
  checkCriticalResources() {
    console.log('[ResourceMonitor] 开始检查关键资源');

    // 检查Font Awesome是否正确加载（支持本地化部署）
    const faStylesheets = Array.from(document.styleSheets).filter(sheet => {
      if (!sheet.href) return false;

      return (
        sheet.href.includes('font-awesome') ||
        sheet.href.includes('fontawesome') ||
        sheet.href.includes('/assets/fonts/') ||
        sheet.href.includes('optimized-fonts') ||
        sheet.href.includes('fa-')
      );
    });

    console.log(`[ResourceMonitor] 找到 ${faStylesheets.length} 个Font Awesome相关样式表`);

    // 列出找到的样式表
    faStylesheets.forEach(sheet => {
      console.log('[ResourceMonitor] Font Awesome样式表:', sheet.href);
    });

    // 检查Font Awesome是否实际工作
    const hasWorkingIcons = this.checkFontAwesomeActuallyWorking();
    const hasInlineFA = this.checkInlineFontAwesome();

    if (faStylesheets.length === 0 && !hasInlineFA && !hasWorkingIcons) {
      console.warn('[ResourceMonitor] 确认Font Awesome未正确加载');
      this.addCompatibilityIssue('fontawesome-missing', 'Font Awesome样式表未找到');
    } else {
      console.log('[ResourceMonitor] Font Awesome检查通过 - 样式表或图标工作正常');
    }

    // 检查关键字体（只在确实有问题时报告）
    this.checkCriticalFonts();
  }

  /**
   * 检查Font Awesome是否实际工作
   */
  checkFontAwesomeWorking() {
    // 创建一个测试图标，检查是否正确显示
    const testIcon = document.createElement('i');
    testIcon.className = 'fas fa-check';
    testIcon.style.position = 'absolute';
    testIcon.style.left = '-9999px';
    testIcon.style.fontSize = '16px';

    document.body.appendChild(testIcon);

    try {
      // 等待一小段时间让样式应用
      setTimeout(() => {
        const computedStyle = window.getComputedStyle(testIcon, '::before');
        const fontFamily = computedStyle.fontFamily;
        const content = computedStyle.content;

        const isWorking = (
          (fontFamily && (fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome'))) ||
          (content && content !== 'none' && content !== '""')
        );

        console.log('[ResourceMonitor] Font Awesome工作状态检查:', {
          fontFamily,
          content,
          isWorking
        });

        document.body.removeChild(testIcon);
        return isWorking;
      }, 100);

      return true; // 假设工作正常，避免误报
    } catch (error) {
      console.warn('[ResourceMonitor] Font Awesome工作状态检查失败:', error);
      if (document.body.contains(testIcon)) {
        document.body.removeChild(testIcon);
      }
      return false;
    }
  }

  /**
   * 检查关键字体
   */
  checkCriticalFonts() {
    if (!('fonts' in document)) {
      console.log('[ResourceMonitor] 浏览器不支持Font Loading API，跳过字体检查');
      return;
    }

    const criticalFonts = ['Font Awesome', 'FontAwesome'];
    let foundFonts = 0;
    let errorFonts = 0;

    criticalFonts.forEach(fontName => {
      const fontFaces = Array.from(document.fonts).filter(font =>
        font.family.includes(fontName)
      );

      if (fontFaces.length > 0) {
        foundFonts++;
        console.log(`[ResourceMonitor] 找到字体: ${fontName}, 数量: ${fontFaces.length}`);

        fontFaces.forEach(fontFace => {
          if (fontFace.status === 'error') {
            errorFonts++;
            console.warn(`[ResourceMonitor] 字体加载失败: ${fontName}, 状态: ${fontFace.status}`);
          } else {
            console.log(`[ResourceMonitor] 字体状态正常: ${fontName}, 状态: ${fontFace.status}`);
          }
        });
      }
    });

    // 只有在确实没有找到任何Font Awesome字体且图标不工作时才报告问题
    if (foundFonts === 0) {
      const hasWorkingIcons = this.checkFontAwesomeWorking();
      if (!hasWorkingIcons) {
        console.warn('[ResourceMonitor] 未找到关键字体且图标不工作');
        this.addCompatibilityIssue('critical-font-missing', '关键字体未找到: Font Awesome');
      } else {
        console.log('[ResourceMonitor] 虽然未找到字体定义，但图标正常工作');
      }
    } else if (errorFonts > 0) {
      console.warn('[ResourceMonitor] 部分字体加载失败');
      this.addCompatibilityIssue('critical-font-error', `${errorFonts} 个关键字体加载失败`);
    } else {
      console.log('[ResourceMonitor] 关键字体检查通过');
    }
  }

  /**
   * 获取浏览器信息
   */
  getBrowserInfo() {
    const ua = navigator.userAgent.toLowerCase();
    let name = 'Unknown';
    let version = 0;

    if (ua.indexOf('chrome') > -1) {
      name = 'Chrome';
      const match = ua.match(/chrome\/(\d+)/);
      version = match ? parseInt(match[1]) : 0;
    } else if (ua.indexOf('firefox') > -1) {
      name = 'Firefox';
      const match = ua.match(/firefox\/(\d+)/);
      version = match ? parseInt(match[1]) : 0;
    } else if (ua.indexOf('safari') > -1) {
      name = 'Safari';
      const match = ua.match(/version\/(\d+)/);
      version = match ? parseInt(match[1]) : 0;
    }

    return { name, version };
  }

  /**
   * 生成兼容性报告
   */
  generateCompatibilityReport() {
    const criticalIssues = this.compatibilityIssues.filter(issue => 
      issue.type.includes('missing') || issue.type.includes('error')
    );

    const warningIssues = this.compatibilityIssues.filter(issue => 
      issue.type.includes('display') || issue.type.includes('slow')
    );

    return {
      hasIssues: this.compatibilityIssues.length > 0,
      hasCriticalIssues: criticalIssues.length > 0,
      totalIssues: this.compatibilityIssues.length,
      criticalIssues: criticalIssues.length,
      warningIssues: warningIssues.length,
      issues: this.compatibilityIssues,
      failedResources: Array.from(this.failedResources),
      timestamp: Date.now()
    };
  }

  /**
   * 获取兼容性摘要
   */
  getCompatibilitySummary() {
    const report = this.generateCompatibilityReport();
    
    return {
      status: report.hasCriticalIssues ? 'critical' : (report.hasIssues ? 'warning' : 'ok'),
      message: report.hasCriticalIssues 
        ? `发现 ${report.criticalIssues} 个严重问题，${report.warningIssues} 个警告`
        : report.hasIssues 
        ? `发现 ${report.warningIssues} 个警告`
        : '所有资源正常加载',
      issues: report.issues
    };
  }

  /**
   * 获取所有问题
   */
  getAllIssues() {
    return this.compatibilityIssues;
  }

  /**
   * 清除问题记录
   */
  clearIssues() {
    this.compatibilityIssues = [];
    this.failedResources.clear();
  }
}

// 创建全局实例（但可能不会初始化）
let resourceLoadingMonitor = null;

// 只在需要时创建实例
const createMonitorIfNeeded = () => {
  if (!resourceLoadingMonitor) {
    resourceLoadingMonitor = new ResourceLoadingMonitor();
  }
  return resourceLoadingMonitor;
};

// 提供一个安全的访问方法
const getResourceMonitor = () => {
  return resourceLoadingMonitor;
};

// 提供启用监控的方法
const enableResourceMonitoring = () => {
  localStorage.setItem('resource-monitoring-enabled', 'true');
  return createMonitorIfNeeded();
};

// 提供禁用监控的方法
const disableResourceMonitoring = () => {
  localStorage.removeItem('resource-monitoring-enabled');
  if (resourceLoadingMonitor) {
    resourceLoadingMonitor.clearIssues();
  }
};

// 内网环境默认完全禁用资源监控，避免误报
// 只有在明确启用或检测到真正的旧浏览器时才创建
if (typeof window !== 'undefined') {
  const hasDebugParam =
    window.location.search.includes('debug=true') ||
    window.location.search.includes('monitor=true') ||
    window.location.search.includes('resource-monitor=true');
  const isExplicitlyEnabled = localStorage.getItem('resource-monitoring-enabled') === 'true';

  // 检测是否为真正需要监控的旧浏览器
  const ua = navigator.userAgent.toLowerCase();
  const isOldBrowser =
    ua.indexOf('trident') > -1 ||
    ua.indexOf('msie') > -1 ||
    (ua.match(/chrome\/(\d+)/) && parseInt(ua.match(/chrome\/(\d+)/)[1]) < 60) ||
    (ua.match(/firefox\/(\d+)/) && parseInt(ua.match(/firefox\/(\d+)/)[1]) < 60) ||
    (ua.match(/version\/(\d+).*safari/) && parseInt(ua.match(/version\/(\d+).*safari/)[1]) < 12);

  const shouldAutoCreate = hasDebugParam || isExplicitlyEnabled || isOldBrowser;

  if (shouldAutoCreate) {
    console.log('[ResourceMonitor] 启用资源监控:', {
      reason: hasDebugParam ? 'debug参数' : isExplicitlyEnabled ? '明确启用' : '旧浏览器',
      userAgent: navigator.userAgent
    });
    resourceLoadingMonitor = new ResourceLoadingMonitor();
  } else {
    console.log('[ResourceMonitor] 资源监控已禁用 (内网环境默认设置)');
    console.log('[ResourceMonitor] 如需启用请添加 ?debug=true 参数或调用 enableResourceMonitoring()');
  }
}

export default getResourceMonitor();
export {
  ResourceLoadingMonitor,
  getResourceMonitor,
  enableResourceMonitoring,
  disableResourceMonitoring,
  createMonitorIfNeeded
};
